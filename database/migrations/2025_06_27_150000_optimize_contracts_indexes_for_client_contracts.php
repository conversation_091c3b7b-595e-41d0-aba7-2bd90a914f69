<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Optimizes indexes for the showClientContracts method to handle 7000+ clients efficiently
     */
    public function up(): void
    {
        // Add composite index for optimal performance in showClientContracts query
        // This index covers: WHERE owner_id = ? AND parent_id IS NULL ORDER BY created_at DESC
        DB::statement('CREATE INDEX IF NOT EXISTS idx_contracts_owner_parent_created ON contracts (owner_id, parent_id, created_at DESC)');
        
        // Add index for client_vendor_id to optimize the JOIN with clientVendor relationship
        DB::statement('CREATE INDEX IF NOT EXISTS idx_contracts_client_vendor_id ON contracts (client_vendor_id)');
        
        // Add composite index for company_clients table to optimize validation queries
        DB::statement('CREATE INDEX IF NOT EXISTS idx_company_clients_company_client ON company_clients (company_id, client_id)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP INDEX IF EXISTS idx_contracts_owner_parent_created');
        DB::statement('DROP INDEX IF EXISTS idx_contracts_client_vendor_id');
        DB::statement('DROP INDEX IF EXISTS idx_company_clients_company_client');
    }
};
