<?php

namespace App\Services\Company;

use App\Enums\AppEnviroment;
use App\Enums\Company\CompanyInviteTypeEnum;
use App\Enums\Company\CompanyType;
use App\Enums\Customer\CustomerPartnershipTypesEnum;
use App\Enums\Lookup\LookupOptionsEnum;
use App\Models\Company\Company;
use App\Models\Company\CompanyClient;
use App\Models\Company\CompanyInvite;
use App\Models\Lookup\LookupOptionValue;
use App\Models\User;
use App\Services\AppConfig;
use App\Services\AuthService;
use BenSampo\Enum\Exceptions\InvalidEnumMemberException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class CompanyClientService
{
    /**
     * Validates that the company can manage clients, if not it will throw an error
     *
     * @throws ValidationException
     */
    public static function validateCompanyCanManageClients(Company $company): void
    {
        if (!$company->manage_clients) {
            throw ValidationException::withMessages([
                'manage_clients' => config('genericMessages.error.COMPANY_NOT_ALLOWED_TO_HAVE_CLIENTS'),
            ]);
        }
    }

    /**
     * Creates a new company client associated with a parent company.
     *
     * This method retrieves or creates a company with the type `MSP_CLIENT` and links it to the given parent company.
     * It sets company properties, assigns a subdomain, and establishes a relationship through `CompanyClient`.
     *
     * @param  Company  $parent  The parent company to associate the client with.
     * @param  string  $name  The name of the client company.
     * @param  string|null  $industry  The industry of the client company (optional, defaults to an empty string).
     * @param  User|null  $loggedUser  The user performing the operation (optional).
     * @param  int|null  $numberOfEndpoints  The number of endpoints for the client (optional, defaults to 0).
     * @param  string|null  $partnershipTypeId  The ID of the partnership type (optional).
     * @param  string|null  $otherPartnershipType  Additional details about the partnership type (optional).
     * @param  bool  $plaidIntegrationEnabled  Indicates if Plaid integration is enabled (optional).
     * @return Company The created or retrieved client company.
     *
     * @throws ValidationException
     */
    public static function createCompanyClient(
        Company $parent,
        string $name,
        ?string $industry = '',
        ?User $loggedUser = null,
        ?int $numberOfEndpoints = 0,
        ?string $partnershipTypeId = null,
        ?string $otherPartnershipType = null,
        ?bool $plaidIntegrationEnabled = false,
    ): Company {
        $client = CompanyService::createCompany($name, CompanyType::MSP_CLIENT);
        $client->industry = $industry;
        $client->subdomain = CompanyService::getCompanySubdomain($client);
        $client->company_profile_types_id = $parent->company_profile_types_id;
        $client->created_by = AuthService::getLoggedInUserId() ?? null;
        $client->updated_by = AuthService::getLoggedInUserId() ?? null;
        $client->save();
        $client->number_of_endpoints = $numberOfEndpoints;
        CompanyClient::updateOrCreate([
            'company_id' => $parent->id,
            'client_id' => $client->id,
        ],[
            'number_of_endpoints' => $numberOfEndpoints,
            'author_id' => $loggedUser?->id ?? null,
            'partnership_type_id' => $partnershipTypeId,
            'other_partnership_type' => $otherPartnershipType,
            'plaid_integration_enabled' => $plaidIntegrationEnabled,
        ]);
        CompanyService::appendVendorContractStatus($client);

        return $client;
    }

    public static function encodeInviteSignature(
        string $entityId,
        ?string $inviteType = null
    ): string {
        $type = empty($inviteType) ? '' : '*AAN*' . $inviteType;

        return base64_encode($entityId . $type);
    }

    /**
     * Decodes an invitation signature and extracts relevant information.
     *
     * The signature is expected to be a base64-encoded string containing parts separated by "*AAN*".
     * The method processes different formats of invitation signatures:
     *
     * - **1 part:** Individual invitation (new version), expects a numeric ID referencing a `CompanyInvite`.
     * - **2 parts:** Open invitation, expects the first part to be a numeric `Company` ID.
     * - **3 parts:** Individual invitation (old version), where:
     *     - Part 1 must be a valid email.
     *     - Part 2 and 3 must be valid `Company` IDs.
     *
     * If the signature format is invalid or references non-existent records, a `ValidationException` is thrown.
     *
     * @param  string  $signature  The base64-encoded invitation signature.
     * @return object An object containing extracted invitation details.
     *                - `email` (string|null): The email associated with the invite (only for individual invitations).
     *                - `parent_company_id` (int|null): The ID of the parent company.
     *                - `child_company_id` (int|null): The ID of the child company (only for individual invitations).
     *                - `type` (string): The type of the company to be registered (e.g., MSP_CLIENT).
     *                - `invite_type` (string): The type of invitation (individual|open).
     *
     * @throws ValidationException If the signature format is invalid or the referenced entities do not exist.
     */
    public static function decodeInviteSignature(string $signature): object
    {
        $parts = explode('*AAN*', base64_decode($signature));
        $hasErrors = false;
        if (!$parts) {
            throw ValidationException::withMessages([
                'signature' => config('genericMessages.error.INVALID_INVITATION_SIGNATURE'),
            ]);
        }
        $response = [];
        switch (count($parts)) {
            case 1: // Individual invitation new version
                if (is_numeric($parts[0])) {
                    try {
                        $companyInvite = CompanyInvite::findOrFail($parts[0]);
                        $response = [
                            'email' => $companyInvite->email,
                            'parent_company_id' => $companyInvite->parent_company_id,
                            'child_company_id' => $companyInvite->child_company_id,
                            'type' => $companyInvite->type,
                            'invite_type' => CompanyInviteTypeEnum::individual,
                        ];
                    } catch (\RuntimeException $e) {
                        Log::error(__CLASS__ . '::' . __FUNCTION__ . '::ERROR::' . $e->getMessage());
                        $hasErrors = true;
                    }
                } else {
                    $hasErrors = true;
                }

                break;
            case 2: // Open invitation
                if (is_numeric($parts[0])) {
                    try {
                        $company = Company::findOrFail($parts[0]);
                        $response = [
                            'parent_company_id' => $company->id,
                            'type' => CompanyType::MSP_CLIENT,
                            'invite_type' => CompanyInviteTypeEnum::open,
                        ];
                    } catch (\RuntimeException $e) {
                        Log::error(__CLASS__ . '::' . __FUNCTION__ . '::ERROR::' . $e->getMessage());
                        $hasErrors = true;
                    }
                } else {
                    $hasErrors = true;
                }

                break;
            case 3: // Individual invitation old version
                // This else if is for old signatures before 31 of october 2024, the new signatures only have
                // one part with the invite id
                if (!filter_var($parts[0], FILTER_VALIDATE_EMAIL)) {
                    // Part 1 must be an email
                    $hasErrors = true;
                } else if (!is_numeric($parts[1]) || !is_numeric($parts[2])
                    || !Company::where('id', $parts[1])->exists() || !Company::where('id', $parts[2])->exists()) {
                    // Part 2 and 3 must be a valid company id
                    $hasErrors = true;
                }

                break;
            default:
                $hasErrors = true;

                break;
        }
        if ($hasErrors) {
            throw ValidationException::withMessages([
                'signature' => config('genericMessages.error.INVALID_INVITATION_SIGNATURE'),
            ]);
        }

        return (object)$response;
    }

    /**
     * This function generates the secure frontend url
     *
     * @throws ValidationException
     */
    public static function prepareMSPClientFEUrl(string $subdomain, string $signature): string
    {
        $url = self::getCustomerDomainUrl($subdomain);

        return "{$url}/s/{$signature}";
    }

    /**
     * @throws ValidationException
     */
    public static function getCustomerDomainUrl(
        ?string $subdomain = null
    ): string {
        $protocol = 'https://';
        if (config('app.env') === AppEnviroment::LOCAL) {
            $protocol = 'http://';
        }
        $subdomain = $subdomain === null ? '' : $subdomain . '.';
        $url = $protocol . $subdomain . AppConfig::loadAppConfigByKey('CLIENT_DOMAIN')->value;

        return $url;
    }

    /**
     * Clear the cached filters for a company
     */
    public static function clearCompanyClientFiltersCache(Company $company): void
    {
        $cacheKey = "company_client_filters_{$company->id}";
        Cache::forget($cacheKey);
    }

    /**
     * Clear the clients summary cache for a company
     */
    public static function clearClientsSummaryCache(Company $company): void
    {
        $cacheKey = "company_clients_summary_{$company->id}";
        Cache::forget($cacheKey);
    }

    /**
     * Clear all client-related caches for a company
     * This method clears both filters cache and summary cache
     */
    public static function clearAllClientCaches(Company $company): void
    {
        self::clearCompanyClientFiltersCache($company);
        self::clearClientsSummaryCache($company);
    }

    /**
     * Validates the company is MSP, the company must have the manage_clients flag on and the logged user must be
     * an admin or claimer
     *
     * @throws ValidationException
     */
    public static function clientValidations(Company $company): User
    {
        CompanyService::validateCompanyIsMSP($company);
        self::validateCompanyCanManageClients($company);
        $loggedUser = AuthService::getAuthUser();

        return $loggedUser;
    }

    /**
     * @throws ValidationException
     * @throws InvalidEnumMemberException
     */
    public static function checkClientIsExistsWithCompany($companyClientData, $company): bool
    {
        $isAvailable = true;
        $client = CompanyClient::where('client_id', $companyClientData['client_id'])->first();
        if (!empty($client) && $companyClientData['company_id'] !== '' . $client->company_id) {
            $isAvailable = false;
        }
        if ($isAvailable) {
            CompanyClient::updateOrCreate(['client_id' => $company->id], $companyClientData);
        }

        return $isAvailable;
    }

    /**
     * This function generates the secure register url
     */
    public static function getRegisterInviteUrl(string $companyInviteId): string
    {
        return config('app.fe_url') . '/register?signature='
            . self::encodeInviteSignature($companyInviteId);
    }

    public static function selectPartnershipType(string $partnershipType): array
    {
        $customPartnershipTypes = LookupOptionsEnum::CUSTOMER_PARTNERSHIP_TYPES;
        $lookupOptionValue = LookupOptionValue::whereHas('lookupOption', function ($query) use ($customPartnershipTypes) {
            $query->where('name', $customPartnershipTypes);
        });
        $otherPartnershipType = null;
        switch (strtolower($partnershipType)) {
            case strtolower(CustomerPartnershipTypesEnum::CURRENT_PARTNER):
                $lookupOptionValue->where('name', CustomerPartnershipTypesEnum::CURRENT_PARTNER);

                break;
            case strtolower(CustomerPartnershipTypesEnum::PROSPECT):
                $lookupOptionValue->where('name', CustomerPartnershipTypesEnum::PROSPECT);

                break;

            default:
                $lookupOptionValue->where('name', CustomerPartnershipTypesEnum::OTHER);
                $otherPartnershipType = $partnershipType;

                break;
        }
        $partnershipTypeId = !empty($partnershipType) ? $lookupOptionValue->first()->id : null;

        return [$partnershipTypeId, $otherPartnershipType];
    }

    public static function getPartnershipIdByValue(string $partnershipType): ?int
    {
        $customPartnershipTypes = LookupOptionsEnum::CUSTOMER_PARTNERSHIP_TYPES;

        return LookupOptionValue::whereHas('lookupOption', function ($query) use ($customPartnershipTypes) {
            $query->where('name', $customPartnershipTypes);
        })->where('name', $partnershipType)
            ->first()
            ->id ?? null;
    }
}
