<?php

namespace App\Services\Company;

use App\Enums\Cache\CacheTTLEnum;
use Illuminate\Support\Facades\Cache;

class CompanyClientCacheService
{
    /**
     * Cache key prefix for client contracts
     */
    private const CLIENT_CONTRACTS_CACHE_PREFIX = 'client_contracts_';
    
    /**
     * Cache key prefix for clients summary
     */
    private const CLIENTS_SUMMARY_CACHE_PREFIX = 'clients_summary_';
    
    /**
     * Cache key prefix for company client filters
     */
    private const COMPANY_CLIENT_FILTERS_CACHE_PREFIX = 'company_client_filters_';

    /**
     * Get cache key for client contracts
     */
    public static function getClientContractsCacheKey(int $companyId, array $requestParams = []): string
    {
        $paramsHash = md5(serialize($requestParams));
        return self::CLIENT_CONTRACTS_CACHE_PREFIX . $companyId . '_' . $paramsHash;
    }

    /**
     * Get cache key for clients summary
     */
    public static function getClientsSummaryCacheKey(int $companyId): string
    {
        return self::CLIENTS_SUMMARY_CACHE_PREFIX . $companyId;
    }

    /**
     * Get cache key for company client filters
     */
    public static function getCompanyClientFiltersCacheKey(int $companyId): string
    {
        return self::COMPANY_CLIENT_FILTERS_CACHE_PREFIX . $companyId;
    }

    /**
     * Clear all client-related caches for a company
     */
    public static function clearAllClientCaches(int $companyId): void
    {
        self::clearClientContractsCache($companyId);
        self::clearClientsSummaryCache($companyId);
        self::clearCompanyClientFiltersCache($companyId);
    }

    /**
     * Clear client contracts cache for a company
     */
    public static function clearClientContractsCache(int $companyId): void
    {
        // Clear all cached contract data for this company
        $pattern = self::CLIENT_CONTRACTS_CACHE_PREFIX . $companyId . '_*';
        self::clearCacheByPattern($pattern);
    }

    /**
     * Clear clients summary cache for a company
     */
    public static function clearClientsSummaryCache(int $companyId): void
    {
        $cacheKey = self::getClientsSummaryCacheKey($companyId);
        Cache::forget($cacheKey);
    }

    /**
     * Clear company client filters cache for a company
     */
    public static function clearCompanyClientFiltersCache(int $companyId): void
    {
        $cacheKey = self::getCompanyClientFiltersCacheKey($companyId);
        Cache::forget($cacheKey);
    }

    /**
     * Cache client contracts data
     */
    public static function cacheClientContracts(string $cacheKey, $data): void
    {
        Cache::put($cacheKey, $data, CacheTTLEnum::MEDIUM->value);
    }

    /**
     * Get cached client contracts data
     */
    public static function getCachedClientContracts(string $cacheKey)
    {
        return Cache::get($cacheKey);
    }

    /**
     * Clear cache by pattern (implementation depends on cache driver)
     */
    private static function clearCacheByPattern(string $pattern): void
    {
        // For Redis cache driver
        if (config('cache.default') === 'redis') {
            $keys = Cache::getRedis()->keys($pattern);
            if (!empty($keys)) {
                Cache::getRedis()->del($keys);
            }
        } else {
            // For other cache drivers, we'll need to track keys manually
            // This is a simplified implementation
            $cacheKeys = Cache::get('cache_keys_' . str_replace('*', '', $pattern), []);
            foreach ($cacheKeys as $key) {
                Cache::forget($key);
            }
            Cache::forget('cache_keys_' . str_replace('*', '', $pattern));
        }
    }

    /**
     * Track cache keys for non-Redis drivers
     */
    public static function trackCacheKey(string $cacheKey): void
    {
        if (config('cache.default') !== 'redis') {
            $prefix = explode('_', $cacheKey)[0] . '_' . explode('_', $cacheKey)[1] . '_';
            $trackingKey = 'cache_keys_' . $prefix;
            $keys = Cache::get($trackingKey, []);
            $keys[] = $cacheKey;
            Cache::put($trackingKey, array_unique($keys), CacheTTLEnum::LONG->value);
        }
    }
}
