<?php

namespace App\Http\Requests\Company\Client;

use App\Http\Requests\Admin\AdminSearchRequest;
use App\Models\Company\Company;
use App\Models\Company\CompanyClient;
use Illuminate\Validation\Rule;

class CompanyClientContractsRequest extends AdminSearchRequest
{
    public Company $company;
    private array $validIDs;

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'company_id' => [
                'required',
                'numeric',
                // Optimized: Use database validation instead of in-memory array
                function ($attribute, $value, $fail) {
                    $exists = CompanyClient::where('company_id', $this->company->id)
                        ->where('client_id', $value)
                        ->exists();

                    if (!$exists) {
                        $fail(config('genericMessages.error.CLIENT_NOT_BELONGS_TO_COMPANY'));
                    }
                },
            ],
        ];
    }

    public function prepareForValidation(): void
    {
        $this->company = $this->route('company');
        // Optimized: Use lazy loading instead of loading all client IDs into memory
        // This prevents loading 7000+ records for validation
        $this->validIDs = [];
    }

    public function messages(): array
    {
        return [
            'id.in' => config('genericMessages.error.CLIENT_NOT_BELONGS_TO_COMPANY'),
        ];
    }
}
