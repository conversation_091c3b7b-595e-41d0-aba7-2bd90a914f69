<?php

namespace App\Http\Controllers\Api\Company;

use App\Enums\Cache\CacheTTLEnum;
use App\Enums\Company\CompanyClientFilters;
use App\Enums\Company\CompanyProfileTypes;
use App\Enums\Company\CompanyStatus;
use App\Enums\Company\CompanyType;
use App\Enums\Customer\CustomerPartnershipTypesEnum;
use App\Helpers\CSVHelper;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Company\Client\CompanyClientContractsRequest;
use App\Http\Requests\Company\Client\CompanyClientDeleteRequest;
use App\Http\Requests\Company\Client\CompanyClientShowAllRequest;
use App\Http\Requests\Company\Client\CompanyClientStoreImportFromCSVRequest;
use App\Http\Requests\Company\Client\CompanyClientStoreRequest;
use App\Http\Requests\Company\Client\CompanyClientSubdomainRequest;
use App\Http\Requests\Company\Client\CompanyClientUpdateRequest;
use App\Http\Resources\Company\Client\CompanyClientContractResource;
use App\Http\Resources\Company\Client\CompanyClientCountResource;
use App\Http\Resources\Company\Client\CompanyClientResource;
use App\Http\Resources\Company\Client\CompanyClientsStackResource;
use App\Http\Resources\Company\Client\CompanySummaryResource;
use App\Models\Company\Company;
use App\Models\Company\CompanyClient;
use App\Services\AuthService;
use App\Services\Company\CompanyClaimerResponseService;
use App\Services\Company\CompanyClientService;
use App\Services\Company\CompanyService;
use App\Services\ImageService;
use App\Services\MyStack\CompanyClientStackService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use BenSampo\Enum\Exceptions\InvalidEnumMemberException;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class CompanyClientController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/clients",
     *     operationId="company/{friendly_url}/clients/showAll",
     *     tags={"CompanyClientController"},
     *     summary="Get company clients list",
     *     description="Get company clients list",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/clients
    // OPTIONAL PARAMS
    /*
       ?paged=BOOLEAN&page=PAGE
        &order_by=COLUMN_NAME&sort=SORT_VALUE
        &search_word=SEARCH_WORD
        &number_of_endpoints=ARRAY<numbers>
        &locations=ARRAY<strings>
     *
     * */
    // needs Bearer Token
    public function showAll(CompanyClientShowAllRequest $request, Company $company): AnonymousResourceCollection
    {
        CompanyClientService::clientValidations($company);
        $query = $this->prepareShowAllQuery($request, $company);
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);
        if ($request->has('using_stack')) {
            CompanyClientStackService::appendClientsUsingStackStatus($result, $request->using_stack);
        }
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }
        ImageService::appendCompanyAvatars($pageResults);
        CompanyService::appendVendorContractsStatus($pageResults);
        CompanyClientStackService::appendClientStackCount($pageResults);
        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return CompanyClientResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/clients/count",
     *     operationId="company/{friendly_url}/clients/count",
     *     tags={"CompanyClientController"},
     *     summary="Get company clients list count",
     *     description="Get company clients list count",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/clients/count?as_company_id={companyid}
    // needs Bearer Token
    public function getCustomerCountForCompany(Company $company): CompanyClientCountResource
    {
        $count = CompanyClient::where('company_id', $company->id)->count();

        return new CompanyClientCountResource($count);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/clients/generate-csv",
     *     operationId="/api/v1/company/{friendly_url}/clients/generate-csv",
     *     tags={"CompanyClientController"},
     *     summary="Export company clients list as a CSV file",
     *     description="Export company clients list as a CSV file",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/clients/generate-csv
    // OPTIONAL PARAMS
    /*
       ?order_by=COLUMN_NAME&sort=SORT_VALUE
        &search_word=SEARCH_WORD
        &number_of_endpoints=ARRAY<numbers>
        &locations=ARRAY<strings>
     *
     * */
    // needs Bearer Token
    public function generateCSV(CompanyClientShowAllRequest $request, Company $company)
    {
        $columns = [
            'name', 'location', 'industry', 'amount_of_endpoints', 'partnership_type', 'plaid_integration_enabled',
        ];
        CompanyClientService::clientValidations($company);
        $query = $this->prepareShowAllQuery($request, $company)
            ->orderBy('name');
        $clients = UtilityHelper::getSearchRequestQueryResults($request, $query);
        ImageService::appendCompanyAvatars($clients);
        CompanyService::appendVendorContractsStatus($clients);

        $data = [];
        foreach ($clients as $client) {
            $data[] = [
                $client->name,
                $client?->address ?? '',
                $client->industry,
                $client->number_of_endpoints,
                $client->partnership_type ?? '--',
                $client->plaid_integration_enabled ? 'Enabled' : 'Disabled',
            ];
        }

        $fileName = 'clients-' . time() . '.csv';

        return UtilityHelper::getCSVFileResponse($columns, $data, $fileName);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/clients/download-pdf",
     *     operationId="downloadPDF",
     *     tags={"CompanyClientController"},
     *     summary="Export company clients list as a PDF file",
     *     description="Export company clients list as a PDF file",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/clients/download-pdf
    // OPTIONAL PARAMS
    /*
       ?order_by=COLUMN_NAME&sort=SORT_VALUE
        &search_word=SEARCH_WORD
        &number_of_endpoints=ARRAY<numbers>
        &locations=ARRAY<strings>
     *
     * */
    // needs Bearer Token
    public function downloadPDF(CompanyClientShowAllRequest $request, Company $company)
    {
        CompanyClientService::clientValidations($company);
        $query = $this->prepareShowAllQuery($request, $company)
            ->orderBy('name');
        $clients = UtilityHelper::getSearchRequestQueryResults($request, $query);
        ImageService::appendCompanyAvatars($clients);
        CompanyService::appendVendorContractsStatus($clients);

        $viewData = [
            'clients' => [],
            'company' => $company,
            'date' => date('Y/m/d H:i:s'),
        ];
        foreach ($clients as $client) {
            $viewData['clients'][] = [
                'name' => $client->name,
                'address' => $client?->address ?? '',
                'industry' => $client->industry,
                'amount_of_endpoints' => $client->number_of_endpoints,
                'partnership_type' => $client->partnership_type ?? '--',
                'plaid_integration_enabled' => $client->plaid_integration_enabled ? 'Enabled' : 'Disabled',
            ];
        }

        $pdf = Pdf::loadView('Clients.clients', $viewData)
            ->setPaper('a4', 'landscape');
        $pdf->setOption('enable-javascript', true);
        $pdf->setOption('javascript-delay', 2000);

        $fileName = 'clients-' . time() . '.pdf';

        return $pdf->download($fileName);
    }

    // <editor-fold desc="oa comments">
    /**
     * Show clients with their associated stacks.
     *
     * @OA\Get(
     *     path="company/{company}/clients/stack",
     *     operationId="showClientsStack",
     *     tags={"Company"},
     *     summary="Show clients with stacks",
     *     description="Retrieves all clients associated with their stacks.",
     *
     *     @OA\Parameter(
     *         name="company",
     *         in="path",
     *         description="ID of the company",
     *         required=true,
     *
     *         @OA\Schema(type="integer")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="List of clients with associated stacks.",
     *
     *         @OA\JsonContent(
     *             type="array",
     *
     *             @OA\Items(ref="CompanyClientsStackResource")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - The request was valid, but the server is refusing action."
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Not Found - The requested resource could not be found."
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/clients/stack
    // NO PARAMS REQUIRED
    // needs Bearer Token
    public function showClientsStack(Company $company): AnonymousResourceCollection
    {
        CompanyClientService::clientValidations($company);
        $result = $company->clients()
            ->withPivot('number_of_endpoints')
            ->select('companies.id', 'companies.name', 'companies.profile_vendor_handle',
                'companies.friendly_url', 'companies.subdomain', 'companies.industry', 'companies.address')
            ->with(['myStack', 'avatar'])
            ->get();

        return CompanyClientsStackResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/clients/filters",
     *     operationId="company/{friendly_url}/clients/showAllFilters",
     *     tags={"CompanyClientController"},
     *     summary="Get company clients list",
     *     description="Get company clients list",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/clients/filters
    // NO PARAMS
    // needs Bearer Token
    public function showAllFilters(Company $company): JsonResponse
    {
        CompanyClientService::clientValidations($company);

        // Use caching to avoid repeated expensive queries
        $cacheKey = "company_client_filters_{$company->id}";

        $result = Cache::remember($cacheKey, now()->addSeconds(CacheTTLEnum::CACHE_TTL_HOUR), function () use ($company) {
            // Check if company has any clients first (lightweight query)
            $clientsCount = CompanyClient::where('company_id', $company->id)->count();

            $filters = [];

            if ($clientsCount > 0) {
                // Industry Filter - Use database aggregation instead of loading all records
                $industries = DB::table('company_clients')
                    ->join('companies', 'companies.id', '=', 'company_clients.client_id')
                    ->where('company_clients.company_id', $company->id)
                    ->whereNotNull('companies.industry')
                    ->where('companies.industry', '!=', '')
                    ->select('companies.industry')
                    ->distinct()
                    ->orderBy('companies.industry')
                    ->pluck('companies.industry');

                if ($industries->count() > 0) {
                    $filters['industry'] = CompanyClientFilters::industry;
                    $filters['industry']['items'] = $industries->map(function ($industry) {
                        return [
                            'id' => $industry,
                            'name' => $industry,
                        ];
                    })->values();
                }

                // Partnership Filter - Optimized query
                $defaultOptions = [
                    CustomerPartnershipTypesEnum::CURRENT_PARTNER,
                    CustomerPartnershipTypesEnum::PROSPECT,
                    CustomerPartnershipTypesEnum::OTHER,
                ];

                $otherPartnershipId = CompanyClientService::getPartnershipIdByValue(CustomerPartnershipTypesEnum::OTHER);
                $otherOptions = CompanyClient::select('other_partnership_type')
                    ->where('company_id', $company->id)
                    ->where('partnership_type_id', $otherPartnershipId)
                    ->whereNotNull('other_partnership_type')
                    ->where('other_partnership_type', '!=', '')
                    ->distinct()
                    ->orderBy('other_partnership_type', 'asc')
                    ->pluck('other_partnership_type');

                $defaultOptions = array_merge($defaultOptions, $otherOptions->toArray());
                $filters['partnership_type'] = CompanyClientFilters::partnership_type;
                $filters['partnership_type']['items'] = array_map(function ($value) {
                    return [
                        'id' => $value,
                        'name' => $value,
                    ];
                }, $defaultOptions);

                // Locations Filter - Use database aggregation
                $locations = DB::table('company_clients')
                    ->join('companies', 'companies.id', '=', 'company_clients.client_id')
                    ->where('company_clients.company_id', $company->id)
                    ->whereNotNull('companies.address')
                    ->where('companies.address', '!=', '')
                    ->select('companies.address')
                    ->distinct()
                    ->orderBy('companies.address')
                    ->pluck('companies.address');

                if ($locations->count() > 0) {
                    $filters['locations'] = CompanyClientFilters::locations;
                    $filters['locations']['items'] = $locations->map(function ($location) {
                        return [
                            'id' => $location,
                            'name' => $location,
                        ];
                    })->values();
                }
            }

            return [
                'filters' => $filters,
            ];
        });

        return response()->json($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/clients/sub-domain",
     *     operationId="company/{friendly_url}/clients/generateClientSubdomain",
     *     tags={"CompanyClientController"},
     *     summary="Generate Client Subdomain",
     *     description="Generate Client Subdomain",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/clients/sub-domain
    // BODY
    /*{
    	"name": "required|string|unique:companies|max:191",
    }
    needs Bearer Token*/
    public function generateClientSubdomain(CompanyClientSubdomainRequest $request, Company $company): JsonResponse
    {
        CompanyClientService::clientValidations($company);

        return response()->json(['sub-domain' => UtilityHelper::generateUniqueWord(
            'companies',
            'subdomain',
            $request->name,
            ''
        )]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/clients/summary",
     *     operationId="company/affiliates/clientsSummary",
     *     tags={"CompanyClientController"},
     *     summary="Get company clients summary",
     *     description="Returns the company clients summary",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/clients/summary
    // Bearer token needed
    public function clientsSummary(Company $company): CompanySummaryResource
    {
        CompanyClientService::clientValidations($company);

        // Use caching to improve performance for large datasets
        $cacheKey = "company_clients_summary_{$company->id}";

        $response = Cache::remember($cacheKey, now()->addMinutes(10), function () use ($company) {
            // Use database aggregation instead of loading all records into memory

            // 1. Get registered clients count using database aggregation
            $registeredClients = DB::table('company_clients')
                ->where('company_id', $company->id)
                ->count();

            // 2. Get total number of endpoints using database aggregation
            $numberOfEndPoints = (int) DB::table('company_clients')
                ->where('company_id', $company->id)
                ->sum('number_of_endpoints');

            // 3. Get active vendor contracts count using optimized JOIN query
            $activeVendorContracts = DB::table('contracts')
                ->join('company_clients', 'company_clients.client_id', '=', 'contracts.owner_id')
                ->where('company_clients.company_id', $company->id)
                ->whereNull('contracts.parent_id') // Only contracts without addons
                ->count();

            return (object)[
                'registered_clients' => $registeredClients,
                'active_vendors_contracts' => $activeVendorContracts,
                'number_of_endpoints' => $numberOfEndPoints,
            ];
        });

        return new CompanySummaryResource($response);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/clients/store",
     *     operationId="company/{friendly_url}/clients/store",
     *     tags={"CompanyClientController"},
     *     summary="Store new company client",
     *     description="Creates a new company client",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/clients/store
    // needs Bearer Token
    public function store(CompanyClientStoreRequest $request, Company $company): CompanyClientResource
    {
        $loggedUser = CompanyClientService::clientValidations($company);
        $client = CompanyClientService::createCompanyClient(
            $company,
            $request->name,
            $request->industry,
            $loggedUser,
            $request->number_of_endpoints,
            $request->partnership_type_id ?? null,
            $request->other_partnership_type ?? null,
            $request->plaid_integration_enabled ?? false
        );
        $client->partnership_type_id = $request->partnership_type_id;
        $client->other_partnership_type = $request->other_partnership_type;
        $client->plaid_integration_enabled = $request->plaid_integration_enabled;

        // Clear all client-related caches since a new client was added
        CompanyClientService::clearAllClientCaches($company);

        return new CompanyClientResource($client);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/company/{friendly_url}/clients/update",
     *     operationId="company/{friendly_url}/clients/update",
     *     tags={"CompanyClientController"},
     *     summary="Update category",
     *     description="Returns ok",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/clients/update
    // needs Bearer Token
    public function update(CompanyClientUpdateRequest $request, Company $company): CompanyClientResource
    {
        CompanyClientService::clientValidations($company);
        $companyClient = CompanyClient::with('client')
            ->where('client_id', $request->id)
            ->where('company_id', $company->id)
            ->firstOrFail();
        $client = $companyClient->client;
        if ($request->has('name')) {
            $client->name = $request->name;
        }
        if ($request->has('industry')) {
            $client->industry = $request->industry;
        }
        $client->updated_by = AuthService::getLoggedInUserId();
        $client->save();
        if ($request->has('number_of_endpoints')) {
            $companyClient->number_of_endpoints = $request->number_of_endpoints;
            $companyClient->save();
            $client->number_of_endpoints = $request->number_of_endpoints;
        }
        if ($request->exists('partnership_type_id')) {
            $companyClient->partnership_type_id = $request->partnership_type_id ?? null;
            $companyClient->other_partnership_type = $request->other_partnership_type ?? null;
        }
        if ($request->exists('plaid_integration_enabled')) {
            $companyClient->plaid_integration_enabled = $request->plaid_integration_enabled ?? false;
        }
        $companyClient->save();
        CompanyService::appendVendorContractStatus($client);
        $client->partnership_type_id = $companyClient->partnership_type_id;
        $client->other_partnership_type = $companyClient->other_partnership_type;
        $client->plaid_integration_enabled = $companyClient->plaid_integration_enabled;

        // Clear all client-related caches since client data was updated
        CompanyClientService::clearAllClientCaches($company);

        return new CompanyClientResource($client);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/company/{friendly_url}/clients/delete",
     *     operationId="company/{friendly_url}/clients/delete",
     *     tags={"CompanyClientController"},
     *     summary="Delete existing category",
     *     description="Deletes a record and returns no content",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/clients/delete
    /* BODY
    {
    	"id" : "required|numeric|exists:companies,id",
    }*/
    // needs Bearer Token
    public function delete(CompanyClientDeleteRequest $request, Company $company): JsonResponse
    {
        CompanyClientService::clientValidations($company);
        $companyIDs = $request->has('id') ? [$request->id] : $request->ids;
        Company::whereIn('id', $companyIDs)->delete();

        // Clear all client-related caches since clients were deleted
        CompanyClientService::clearAllClientCaches($company);

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{friendly_url}/clients/add/csv",
     *     operationId="company/clients/add/csv",
     *     tags={"CompanyClientController"},
     *     summary="Loop thru csv file and add clients for MSP",
     *     description="Loop thru csv file and add clients for MSP",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws InvalidEnumMemberException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/clients/add/csv
    // BODY - form-data
    /*
    	csv_import: CSV FILE
    */
    // Bearer token needed*/
    public function addClientsFromCSV(CompanyClientStoreImportFromCSVRequest $request, Company $company): JsonResponse
    {
        $loggedUser = CompanyClientService::clientValidations($company);
        Log::debug('Entering CompanyClientController::addClientsFromCSV for company_id = ' . $company->id);
        $csvFile = $request->file('csv_import');
        if (($handle = fopen($csvFile, 'r')) !== false) {
            Log::debug('CompanyClientController::addClientsFromCSV parsing and validating csv');
            $columns = strtolower(preg_replace('/[^A-Za-z|]/', '', implode('|', CSVHelper::fgetcsv($handle))));
            if ($columns !== 'companyname|numberofendpoints|partnershiptype|plaidintegrationenabled') {
                throw ValidationException::withMessages([
                    config('genericMessages.error.INVALID_CSV_FILE_COLUMNS'),
                ]);
            }
            Log::debug('CompanyClientController::addClientsFromCSV getting company ist from CSV');
            $existClientArray = [];
            $storeClientArray = [];
            while (($data = CSVHelper::fgetcsv($handle)) !== false) {
                $companyName = trim($data[0]);
                $numberOfEndPoints = trim($data[1] ?? '');
                $partnershipType = trim($data[2] ?? '');
                $plaidIntegrationEnabled = trim($data[3] ?? '');
                $checkExistClient = Company::where('name', $companyName)->first();
                if (!empty($checkExistClient)) {
                    $existClientArray[] = $companyName;
                    Log::error('Error happened in addClientsFromCSV on ' . $companyName);
                } else {
                    if (strlen($partnershipType) <= 25) {
                        $storeClientArray[] = $companyName;
                        $client = CompanyService::getCompany($companyName, CompanyType::MSP_CLIENT);
                        $client->subdomain = CompanyService::getCompanySubdomain($client);
                        $client->company_profile_types_id = $company->company_profile_types_id;
                        $client->save();
                        [$partnershipTypeId, $otherPartnershipType] = CompanyClientService::selectPartnershipType($partnershipType);
                        CompanyClient::create([
                            'company_id' => $company->id,
                            'client_id' => $client->id,
                            'number_of_endpoints' => $numberOfEndPoints,
                            'author_id' => $loggedUser->id,
                            'partnership_type_id' => $partnershipTypeId,
                            'other_partnership_type' => $otherPartnershipType,
                            'plaid_integration_enabled' => strtolower($plaidIntegrationEnabled) === 'enabled',
                        ]);
                        $client->number_of_endpoints = $numberOfEndPoints;
                        if (!AuthService::userIsSuperAdmin($loggedUser) && !AuthService::userIsAdmin($loggedUser)) {
                            CompanyClaimerResponseService::addClaimer($client, $loggedUser);
                        }
                        CompanyService::appendVendorContractStatus($client);
                        Log::info('CompanyClientController::addClientsFromCSV client ' . $companyName . ' is added successfully');
                    }
                }
            }
            $storedClients = count($storeClientArray);
            $existingClients = count($existClientArray);
            if ($storedClients === 0 && $existingClients === 0) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.INVALID_FILE_EMPTY'),
                ]);
            }
            $responseMessage = [];
            if (count($storeClientArray) > 0) {
                $message = ($storeClientArray > 1) ? ' clients' : ' client';
                $responseMessage['success_message'] = count($storeClientArray) . $message . ' have been successfully added. ';
                $responseMessage['success_client'] = $storeClientArray;
            }
            if (count($existClientArray) > 0) {
                $message = ($existClientArray > 1) ? ' clients' : ' client';
                $responseMessage['error_message'] = count($existClientArray) . $message . ' encountered errors. ';
                $responseMessage['error_client'] = $existClientArray;
            }

            // Clear all client-related caches since clients were added via CSV
            if (count($storeClientArray) > 0) {
                CompanyClientService::clearAllClientCaches($company);
            }

            return response()->json($responseMessage);
        }

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{friendly_url}/clients/contracts",
     *     operationId="company/{friendly_url}/clients/showClientContracts",
     *     tags={"CompanyClientController"},
     *     summary="Get contracts from a given company client",
     *     description="Get contracts from a given company client",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{friendly_url}/clients/contracts
    // OPTIONAL PARAMS
    // needs Bearer Token
    public function showClientContracts(CompanyClientContractsRequest $request, Company $company): AnonymousResourceCollection
    {
        CompanyClientService::clientValidations($company);

        // Optimized query: Use direct join instead of relationship chaining
        // This prevents loading the entire Company model and uses efficient joins
        $query = Contract::select([
                'contracts.id',
                'contracts.parent_id',
                'contracts.owner_id',
                'contracts.client_vendor_id',
                'contracts.client_product_id',
                'contracts.name',
                'contracts.created_at'
            ])
            ->with(['clientVendor:id,name'])
            ->where('contracts.owner_id', $request->company_id)
            ->whereNull('contracts.parent_id') // Filter out addons
            ->orderBy('contracts.created_at', 'DESC');

        // Force pagination for large datasets to prevent memory issues
        if (!$request->has('paged')) {
            $request->merge(['paged' => true]);
        }

        // Set reasonable default page size if not specified
        if (!$request->has('items_per_page')) {
            $request->merge(['items_per_page' => 50]);
        }

        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);

        return CompanyClientContractResource::collection($result);
    }

    private function prepareShowAllQuery(CompanyClientShowAllRequest $request, Company $company)
    {
        $query = Company::select('companies.id', 'companies.name', 'companies.profile_vendor_handle', 'companies.cancellation_link',
            'companies.friendly_url', 'companies.subdomain', 'companies.industry', 'companies.address',
            'companies.address2', 'companies.company_profile_types_id', 'company_clients.number_of_endpoints',
            'companies.city', 'companies.state_id', 'companies.country_id', 'companies.zip', 'companies.created_at',
            'company_clients.plaid_integration_enabled',
            'lookup_option_values.id as partnership_type_id',
            'lookup_option_values.name as partnership_type_name',
            DB::raw("
                CASE 
                    WHEN lookup_option_values.name = '" . CustomerPartnershipTypesEnum::OTHER . "' THEN company_clients.other_partnership_type
                    ELSE lookup_option_values.name
                END AS partnership_type
            ")
        )
            ->withCount('contractsWithoutAddons as contracts_count')
            ->withCount('recentContracts as recent_contracts')
            ->with('companyProfileType:id', 'state')
            ->join('company_clients', 'company_clients.client_id', '=', 'companies.id')
            ->join('company_profile_types', 'company_profile_types.id', '=', 'companies.company_profile_types_id')
            ->leftJoin('lookup_option_values', 'lookup_option_values.id', '=', 'company_clients.partnership_type_id')
            ->when($request->has('partnership_type'), function ($query) use ($request) {
                foreach ($request->partnership_type as $value) {
                    if (in_array($value, [
                        CustomerPartnershipTypesEnum::CURRENT_PARTNER,
                        CustomerPartnershipTypesEnum::PROSPECT,
                        CustomerPartnershipTypesEnum::OTHER,
                    ])) {
                        $query->where('lookup_option_values.name', $value);
                    } else {
                        $query->where('company_clients.other_partnership_type', $value);
                    }
                }
            })->when($request->has('search_word'), function ($query) use ($request) {
                $query->whereRaw("(lower(companies.name) like '%" . strtolower($request->search_word) . "%')");
            })->when($request->has('number_of_endpoints'), function ($query) use ($request) {
                $query->whereIn('company_clients.number_of_endpoints', $request->number_of_endpoints);
            })->when($request->has('vendor_contracts_status'), function ($query) use ($request) {
                $query->where('company_profile_types.value',
                    $request->vendor_contracts_status === CompanyStatus::active
                        ? CompanyProfileTypes::MSPBusinessPremium
                        : CompanyProfileTypes::MSPBusinessBasic);
            })->when($request->query('start_date') && $request->query('end_date'), function ($query) use ($request) {
                $startDate = Carbon::parse($request->query('start_date'))->startOfDay();
                $endDate = Carbon::parse($request->query('end_date'))->endOfDay();
                $query->whereBetween('company_clients.created_at', [$startDate, $endDate]);
            })->when($request->has('locations'), function ($query) use ($request) {
                $query->whereIn('companies.address', $request->locations);
            })->when($request->has('industry'), function ($query) use ($request) {
                $query->whereIn('companies.industry', $request->industry);
            })->when(!$request->has('order_by'), function ($query) {
                $query->orderByRaw('lower(companies.name) ASC');
            })->where('company_clients.company_id', $company->id)
            ->groupBy(['companies.id', 'company_clients.number_of_endpoints', 'lookup_option_values.id',
                'lookup_option_values.name', 'company_clients.other_partnership_type',
                'company_clients.plaid_integration_enabled']);

        return $query;
    }
}
