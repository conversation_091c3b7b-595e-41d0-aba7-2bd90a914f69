<?php

namespace Tests\Feature\Company;

use App\Models\Company\Company;
use App\Models\Company\CompanyClient;
use App\Models\Contract\Contract;
use App\Models\Contract\ClientVendor;
use App\Models\User;
use App\Services\Company\CompanyClientCacheService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class CompanyClientContractsOptimizationTest extends TestCase
{
    use RefreshDatabase;

    private Company $company;
    private Company $clientCompany;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->user = User::factory()->create();
        $this->company = Company::factory()->create();
        $this->clientCompany = Company::factory()->create();
        
        // Create company-client relationship
        CompanyClient::create([
            'company_id' => $this->company->id,
            'client_id' => $this->clientCompany->id,
            'author_id' => $this->user->id,
            'number_of_endpoints' => 100,
        ]);
    }

    /** @test */
    public function it_validates_client_belongs_to_company_efficiently()
    {
        // Enable query logging
        DB::enableQueryLog();
        
        $response = $this->actingAs($this->user)
            ->postJson("/api/v1/company/{$this->company->friendly_url}/clients/contracts", [
                'company_id' => $this->clientCompany->id,
                'paged' => true,
                'page' => 1,
                'items_per_page' => 10,
            ]);
        
        $queries = DB::getQueryLog();
        
        // Verify that we don't have a query that loads all client IDs
        $hasExpensiveQuery = collect($queries)->contains(function ($query) {
            return str_contains($query['query'], 'pluck') || 
                   (str_contains($query['query'], 'company_clients') && !str_contains($query['query'], 'exists'));
        });
        
        $this->assertFalse($hasExpensiveQuery, 'Should not have expensive pluck query for validation');
        
        DB::disableQueryLog();
    }

    /** @test */
    public function it_returns_paginated_contracts_efficiently()
    {
        // Create test contracts
        $clientVendor = ClientVendor::factory()->create();
        
        Contract::factory()->count(100)->create([
            'owner_id' => $this->clientCompany->id,
            'client_vendor_id' => $clientVendor->id,
            'parent_id' => null, // Main contracts, not addons
        ]);
        
        // Create some addon contracts (should be filtered out)
        $parentContract = Contract::factory()->create([
            'owner_id' => $this->clientCompany->id,
            'client_vendor_id' => $clientVendor->id,
            'parent_id' => null,
        ]);
        
        Contract::factory()->count(10)->create([
            'owner_id' => $this->clientCompany->id,
            'client_vendor_id' => $clientVendor->id,
            'parent_id' => $parentContract->id, // These are addons
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/v1/company/{$this->company->friendly_url}/clients/contracts?" . http_build_query([
                'company_id' => $this->clientCompany->id,
                'paged' => true,
                'page' => 1,
                'items_per_page' => 20,
            ]));

        $response->assertStatus(200);
        
        $data = $response->json('data');
        
        // Should return paginated results
        $this->assertCount(20, $data);
        
        // Verify pagination meta exists
        $this->assertArrayHasKey('links', $response->json());
        $this->assertArrayHasKey('meta', $response->json());
        
        // Verify no addon contracts are included
        foreach ($data as $contract) {
            $this->assertNull($contract['parent_id'] ?? null);
        }
    }

    /** @test */
    public function it_forces_pagination_for_large_datasets()
    {
        $clientVendor = ClientVendor::factory()->create();
        
        Contract::factory()->count(200)->create([
            'owner_id' => $this->clientCompany->id,
            'client_vendor_id' => $clientVendor->id,
            'parent_id' => null,
        ]);

        // Request without pagination parameters
        $response = $this->actingAs($this->user)
            ->getJson("/api/v1/company/{$this->company->friendly_url}/clients/contracts?" . http_build_query([
                'company_id' => $this->clientCompany->id,
            ]));

        $response->assertStatus(200);
        
        // Should be automatically paginated with default page size
        $data = $response->json('data');
        $this->assertLessThanOrEqual(50, count($data)); // Default page size is 50
        
        // Should have pagination meta
        $this->assertArrayHasKey('links', $response->json());
        $this->assertArrayHasKey('meta', $response->json());
    }

    /** @test */
    public function it_caches_contract_results()
    {
        Cache::flush(); // Clear any existing cache
        
        $clientVendor = ClientVendor::factory()->create();
        
        Contract::factory()->count(10)->create([
            'owner_id' => $this->clientCompany->id,
            'client_vendor_id' => $clientVendor->id,
            'parent_id' => null,
        ]);

        $requestParams = [
            'company_id' => $this->clientCompany->id,
            'paged' => true,
            'page' => 1,
            'items_per_page' => 10,
        ];

        // First request - should hit database
        $response1 = $this->actingAs($this->user)
            ->getJson("/api/v1/company/{$this->company->friendly_url}/clients/contracts?" . http_build_query($requestParams));

        $response1->assertStatus(200);
        
        // Verify cache was created
        $cacheKey = CompanyClientCacheService::getClientContractsCacheKey(
            $this->clientCompany->id, 
            ['paged' => true, 'page' => 1, 'items_per_page' => 10]
        );
        
        $this->assertNotNull(Cache::get($cacheKey));

        // Second request - should hit cache
        $response2 = $this->actingAs($this->user)
            ->getJson("/api/v1/company/{$this->company->friendly_url}/clients/contracts?" . http_build_query($requestParams));

        $response2->assertStatus(200);
        
        // Results should be identical
        $this->assertEquals($response1->json('data'), $response2->json('data'));
    }

    /** @test */
    public function it_clears_cache_correctly()
    {
        Cache::flush();
        
        // Create cache entry
        $cacheKey = CompanyClientCacheService::getClientContractsCacheKey($this->company->id, []);
        CompanyClientCacheService::cacheClientContracts($cacheKey, ['test' => 'data']);
        
        $this->assertNotNull(Cache::get($cacheKey));
        
        // Clear cache
        CompanyClientCacheService::clearClientContractsCache($this->company->id);
        
        // Cache should be cleared
        $this->assertNull(Cache::get($cacheKey));
    }

    /** @test */
    public function it_handles_invalid_company_id_efficiently()
    {
        DB::enableQueryLog();
        
        $response = $this->actingAs($this->user)
            ->postJson("/api/v1/company/{$this->company->friendly_url}/clients/contracts", [
                'company_id' => 99999, // Non-existent client
                'paged' => true,
            ]);
        
        $queries = DB::getQueryLog();
        
        // Should have validation error
        $response->assertStatus(422);
        
        // Should only have efficient exists query, not expensive pluck
        $validationQueries = collect($queries)->filter(function ($query) {
            return str_contains($query['query'], 'company_clients');
        });
        
        $this->assertCount(1, $validationQueries);
        $this->assertTrue(str_contains($validationQueries->first()['query'], 'exists'));
        
        DB::disableQueryLog();
    }
}
